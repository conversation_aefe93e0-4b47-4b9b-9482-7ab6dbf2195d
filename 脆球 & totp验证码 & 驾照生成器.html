<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ 综合工具箱</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --purple-color: #722ed1;
            --error-color: #f5222d;
            --warning-color: #faad14;
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --shadow-light: 0 2px 8px rgba(0,0,0,0.05);
            --shadow-medium: 0 4px 16px rgba(0,0,0,0.1);
            --shadow-heavy: 0 8px 32px rgba(0,0,0,0.15);
            --bg-primary: #f8fafc;
            --bg-card: #ffffff;
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --border-color: #e2e8f0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 10px;
            position: relative;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-heavy);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 20px);
        }
        
        /* 简化的导航栏 */
        .nav-tabs {
            display: flex;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0 20px;
        }
        
        .nav-tab {
            flex: 1;
            max-width: 200px;
            padding: 16px 24px;
            background: none;
            border: none;
            color: white;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border-radius: 12px 12px 0 0;
            margin: 8px 4px 0 4px;
        }
        
        .nav-tab::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: rgba(255, 255, 255, 0.9);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .nav-tab.active {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
        }
        
        .nav-tab.active::before {
            transform: scaleX(1);
        }
        
        .nav-tab:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .content {
            flex: 1;
            padding: 20px;
            background: var(--bg-primary);
            overflow-y: auto; /* 允许内容区域滚动 */
            max-height: calc(100vh - 80px); /* 限制最大高度 */
            
            /* 美化主内容区域滚动条 */
            scrollbar-width: auto;
            scrollbar-color: #718096 #f7fafc;
        }

        /* 主内容区域滚动条样式 */
        .content::-webkit-scrollbar {
            width: 14px;
            background: #e2e8f0;
        }

        .content::-webkit-scrollbar-track {
            background: #e2e8f0;
            border-radius: 7px;
        }

        .content::-webkit-scrollbar-thumb {
            background: #718096;
            border-radius: 7px;
            border: 2px solid #e2e8f0;
        }

        .content::-webkit-scrollbar-thumb:hover {
            background: #4a5568;
        }

        /* 保持原有内边距，通过滚动解决空间问题 */
        
        .tab-content {
            display: none;
            animation: fadeIn 0.4s ease-out;
            height: 100%;
        }
        
        .tab-content.active {
            display: flex;
            flex-direction: column;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .section {
            background: var(--bg-card);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 16px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }
        
        .section:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-medium);
        }
        
        .section h3 {
            margin-bottom: 16px;
            color: var(--text-primary);
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 8px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9em;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: var(--transition);
            background: var(--bg-card);
            color: var(--text-primary);
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
            background: var(--bg-card);
        }
        
        .btn {
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            border: none;
            padding: 10px 18px; /* 稍微调小按钮 */
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px; /* 稍微调小字体 */
            font-weight: 600;
            margin: 4px;
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-success { 
            background: linear-gradient(135deg, var(--success-color), #73d13d);
            box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
        }
        
        .btn-success:hover {
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
        }
        
        .btn-warning { 
            background: linear-gradient(135deg, var(--warning-color), #ffc53d);
            box-shadow: 0 2px 8px rgba(250, 173, 20, 0.3);
        }
        
        .btn-warning:hover {
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.4);
        }
        
        .btn-purple { 
            background: linear-gradient(135deg, var(--purple-color), #9254de);
            box-shadow: 0 2px 8px rgba(114, 46, 209, 0.3);
        }
        
        .btn-purple:hover {
            box-shadow: 0 4px 12px rgba(114, 46, 209, 0.4);
        }
        
        .status {
            padding: 12px 16px;
            margin: 12px 0;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            border-left: 4px solid transparent;
        }
        
        .status.success { 
            background: #f6ffed; 
            color: #389e0d; 
            border-left-color: var(--success-color);
        }
        .status.error { 
            background: #fff2f0; 
            color: #cf1322; 
            border-left-color: var(--error-color);
        }
        .status.info { 
            background: #f0f9ff; 
            color: #0c5460; 
            border-left-color: var(--primary-color);
        }
        .status.warning { 
            background: #fffbe6; 
            color: #d48806; 
            border-left-color: var(--warning-color);
        }
        
        .loading {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 邮箱布局优化 */
        .email-layout {
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: 20px;
            /* 移除高度限制，让内容自然展开 */
        }

        .email-left-panel {
            display: flex;
            flex-direction: column;
            gap: 16px;
            height: 100%;
            /* 移除最小高度限制，让内容自然展开 */
        }

        .email-right-panel {
            display: flex;
            flex-direction: column;
            gap: 16px;
            height: 100%;
            min-width: 0;
        }

        .email-settings-section {
            flex: 0 0 auto;
            height: 280px; /* 和邮件列表高度对齐 */
            padding: 16px !important; /* 确保内部有足够间距 */
            overflow: hidden; /* 确保内容不会溢出卡片 */
            position: relative; /* 建立定位上下文 */
        }

        .totp-section {
            flex: 0 0 auto; /* 改为固定高度，不占用剩余空间 */
            height: 320px; /* 和邮件内容区域高度一样 */
            padding: 16px !important; /* 确保内部有足够间距 */
            overflow: hidden; /* 确保内容不会溢出卡片 */
            position: relative; /* 建立定位上下文 */
        }

        /* 确保按钮容器在卡片内部 */
        .email-settings-section .btn,
        .totp-section .btn {
            position: relative;
            z-index: 1;
            max-width: calc(100% - 16px); /* 确保按钮不超出卡片宽度，留出边距 */
            word-wrap: break-word; /* 防止文字溢出 */
        }

        /* 强化卡片边界视觉效果 */
        .email-settings-section,
        .totp-section {
            border: 2px solid var(--border-color) !important;
            box-shadow: var(--shadow-light), inset 0 0 0 1px rgba(255,255,255,0.5) !important;
        }

        .email-list-section {
            flex: 0 0 auto;
            height: 280px; /* 恢复固定高度 */
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 防止溢出 */
        }

        .email-detail-section {
            flex: 0 0 auto;
            height: 320px; /* 恢复固定高度 */
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 防止溢出 */
        }

        /* 优化滚动区域 */
        .scrollable-content {
            flex: 1; /* 占满剩余空间 */
            overflow-y: auto;
            overflow-x: hidden;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-card);
            max-height: 100%; /* 确保不超出父容器 */
            box-sizing: border-box; /* 包含边框在尺寸内 */
            
            /* 美化滚动条 - Firefox */
            scrollbar-width: auto;
            scrollbar-color: #718096 #f7fafc;
        }

        /* Webkit浏览器滚动条样式 */
        .scrollable-content::-webkit-scrollbar {
            width: 12px;
            background: #f7fafc;
        }

        .scrollable-content::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 6px;
            margin: 2px;
        }

        .scrollable-content::-webkit-scrollbar-thumb {
            background: #a0aec0;
            border-radius: 6px;
            border: 2px solid #f7fafc;
            min-height: 20px;
        }

        .scrollable-content::-webkit-scrollbar-thumb:hover {
            background: #718096;
        }

        .scrollable-content::-webkit-scrollbar-thumb:active {
            background: #4a5568;
        }

        /* 强制显示滚动条 */
        .scrollable-content {
            overflow-y: scroll !important;
        }

        #emailList {
            padding: 12px;
            min-height: 200px; /* 确保有足够高度显示滚动条 */
            box-sizing: border-box; /* 包含padding在尺寸内 */
            max-width: 100%; /* 防止水平溢出 */
        }

        #emailDetail {
            padding: 16px;
            line-height: 1.6;
            min-height: 200px; /* 确保有足够高度显示滚动条 */
            box-sizing: border-box; /* 包含padding在尺寸内 */
            max-width: 100%; /* 防止水平溢出 */
            word-wrap: break-word; /* 防止长内容溢出 */
        }

        /* 增强滚动条可见性 */
        .scrollable-content::-webkit-scrollbar {
            width: 14px !important;
            background: #e2e8f0 !important;
        }

        .scrollable-content::-webkit-scrollbar-track {
            background: #e2e8f0 !important;
            border-radius: 7px !important;
        }

        .scrollable-content::-webkit-scrollbar-thumb {
            background: #718096 !important;
            border-radius: 7px !important;
            border: 2px solid #e2e8f0 !important;
        }

        .scrollable-content::-webkit-scrollbar-thumb:hover {
            background: #4a5568 !important;
        }

        /* 邮件列表项样式优化 */
        .email-item {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: var(--transition);
            border-radius: 6px;
            margin-bottom: 8px;
            word-wrap: break-word; /* 防止长文本溢出 */
            overflow-wrap: break-word; /* 兼容性更好的断词 */
            box-sizing: border-box; /* 包含padding在尺寸内 */
        }

        .email-item:hover {
            background-color: #f7fafc;
            transform: translateX(2px);
        }

        .email-item:last-child {
            border-bottom: none;
        }

        .email-subject {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 6px;
            font-size: 14px;
            word-wrap: break-word; /* 防止长主题溢出 */
            overflow: hidden; /* 隐藏溢出内容 */
            text-overflow: ellipsis; /* 显示省略号 */
            white-space: nowrap; /* 单行显示 */
        }

        .email-meta {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.4;
            word-wrap: break-word; /* 防止长地址溢出 */
        }



        /* 响应式设计 */
        @media (max-width: 1200px) {
            .email-layout {
                grid-template-columns: 320px 1fr;
                gap: 16px;
            }
        }

        /* 窗口高度适配 - 保持固定高度，让整体滚动 */

        @media (max-width: 900px) {
            body { padding: 5px; }
            .container { 
                border-radius: 8px; 
                min-height: calc(100vh - 10px); 
            }
            .content { padding: 12px; }
            .nav-tab { 
                padding: 12px 16px; 
                font-size: 14px; 
                margin: 4px 2px 0 2px;
            }
            .section { 
                padding: 16px; 
                margin-bottom: 12px; 
            }

                         .email-layout {
                 display: flex;
                 flex-direction: column;
                 gap: 12px;
                 /* 移动端也让内容自然展开，通过父级滚动 */
             }

             .email-left-panel, .email-right-panel {
                 gap: 12px;
             }

             .email-settings-section {
                 flex: 0 0 auto;
                 height: 250px; /* 移动端和邮件列表高度对齐 */
                 padding: 12px !important; /* 移动端减小内边距 */
                 overflow: hidden; /* 确保内容不会溢出卡片 */
                 position: relative; /* 建立定位上下文 */
             }

             .totp-section {
                 flex: 0 0 auto;
                 height: 300px; /* 移动端和邮件详情高度对齐 */
                 padding: 12px !important; /* 移动端减小内边距 */
                 overflow: hidden; /* 确保内容不会溢出卡片 */
                 position: relative; /* 建立定位上下文 */
             }

             .email-list-section {
                 flex: 0 0 auto;
                 height: 250px;
                 overflow: hidden; /* 防止移动端溢出 */
             }

             .email-detail-section {
                 flex: 0 0 auto;
                 height: 300px;
                 overflow: hidden; /* 防止移动端溢出 */
             }

             .scrollable-content {
                 height: calc(100% - 50px) !important;
             }

                         /* 移动端TOTP验证码显示优化 */
             .totp-section #token-email {
                 font-size: 19px !important;
                 letter-spacing: 1.5px !important;
                 padding: 6px 10px !important;
                 min-width: 120px !important;
             }
             
             /* 移动端按钮优化 */
             .email-settings-section .btn,
             .totp-section .btn {
                 font-size: 10px !important;
                 padding: 6px 10px !important;
             }
             
             /* 移动端邮箱设置按钮布局 */
             .email-settings-section .btn {
                 flex: 1 !important;
                 min-width: 70px !important;
             }

             /* 移动端输入框优化 */
             .email-settings-section input,
             .email-settings-section select,
             .totp-section input {
                 font-size: 11px !important;
                 padding: 6px 8px !important;
             }

             /* 移动端标题优化 */
             .email-settings-section h3,
             .totp-section h3 {
                 font-size: 13px !important;
                 margin-bottom: 8px !important;
             }

             /* 移动端TOTP卡片特别优化 */
             .totp-section {
                 padding: 10px !important;
             }

             /* 移动端TOTP密钥输入框 */
             .totp-section #secret-input-email {
                 font-size: 9px !important;
                 padding: 5px 6px !important;
             }

             /* 移动端TOTP按钮优化 */
             .totp-section .btn {
                 font-size: 11px !important;
                 padding: 7px 15px !important;
                 min-width: 100px !important;
             }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="switchTab('email')">📮 邮箱查看器</button>
            <button class="nav-tab" onclick="switchTab('license')">🆔 驾照生成器</button>
        </div>
        
        <div class="content">
            <!-- 邮箱查看器标签页 -->
            <div id="email-tab" class="tab-content active">
                <div id="email-status" class="status info">
                    📬 输入邮箱地址并点击"获取邮件"开始查看
                </div>

                <div class="email-layout">
                    <!-- 左侧：邮箱设置和TOTP -->
                    <div class="email-left-panel">
                        <!-- 邮箱设置 -->
                        <div class="section email-settings-section">
                            <div style="height: 100%; display: flex; flex-direction: column; padding: 12px;">
                                <!-- 紧凑标题 -->
                                <h3 style="margin: 0 0 12px 0; color: var(--primary-color); font-size: 15px; font-weight: 700; text-align: center; padding-bottom: 6px; border-bottom: 1px solid var(--border-color);">📮 邮箱设置</h3>

                                <!-- 紧凑输入区域 -->
                                <div style="flex: 1; display: flex; flex-direction: column; gap: 8px;">
                                    <div>
                                        <label for="emailAddress" style="display: block; margin-bottom: 4px; font-weight: 600; color: var(--text-primary); font-size: 12px;">📧 邮箱地址</label>
                                        <input type="email" id="emailAddress" placeholder="输入邮箱地址"
                                               style="width: 100%; padding: 8px 10px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px; background: var(--bg-card); color: var(--text-primary); box-sizing: border-box;">
                                    </div>

                                    <div>
                                        <label for="timeRange" style="display: block; margin-bottom: 4px; font-weight: 600; color: var(--text-primary); font-size: 12px;">⏰ 时间范围</label>
                                        <select id="timeRange" style="width: 100%; padding: 8px 10px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px; background: var(--bg-card); color: var(--text-primary); box-sizing: border-box;">
                                            <option value="5" selected>最近5分钟</option>
                                            <option value="10">最近10分钟</option>
                                            <option value="30">最近30分钟</option>
                                            <option value="60">最近1小时</option>
                                            <option value="360">最近6小时</option>
                                            <option value="1440">最近24小时</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 固定底部按钮区域 -->
                                <div style="margin-top: 12px; padding: 8px; background: #f8fafc; border-radius: 4px; border: 1px solid var(--border-color);">
                                    <div style="display: flex; gap: 6px;">
                                        <button class="btn btn-success" onclick="checkEmails()" 
                                                style="flex: 1; margin: 0; padding: 8px 12px; font-size: 11px; font-weight: 600; box-sizing: border-box; white-space: nowrap;">
                                            🔄 接收邮件
                                        </button>
                                        <button class="btn" onclick="checkAllEmails()" 
                                                style="flex: 1; margin: 0; padding: 8px 12px; font-size: 11px; font-weight: 600; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3); box-sizing: border-box; white-space: nowrap;">
                                            📬 接收全部
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- TOTP验证码生成器 -->
                        <div class="section totp-section">
                            <div style="height: 100%; display: flex; flex-direction: column; padding: 10px;">
                                <!-- 简洁标题 -->
                                <div style="text-align: center; margin-bottom: 8px;">
                                    <h3 style="margin: 0; color: var(--purple-color); font-size: 14px; font-weight: 700;">🔐 TOTP验证码生成器</h3>
                                </div>

                                <!-- 密钥输入区域 -->
                                <div style="margin-bottom: 8px;">
                                    <input type="text" id="secret-input-email" placeholder="请输入TOTP密钥 (Base32格式)"
                                           style="width: 100%; padding: 6px 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 10px; font-family: 'SF Mono', 'Monaco', monospace; background: var(--bg-card); color: var(--text-primary); box-sizing: border-box; letter-spacing: 0.5px; text-align: center;">
                                    <div id="secret-status-email" style="margin-top: 3px; font-size: 9px; min-height: 10px; text-align: center; font-weight: 500;"></div>
                                </div>

                                                                 <!-- 验证码显示核心区域 -->
                                 <div style="flex: 1; display: flex; flex-direction: column; justify-content: space-between; background: linear-gradient(135deg, #f8fafc, #edf2f7); border-radius: 6px; border: 1px solid var(--border-color); margin-bottom: 6px; padding: 10px 8px; min-height: 100px;">
                                     <!-- 验证码显示 -->
                                     <div style="text-align: center; flex: 1; display: flex; flex-direction: column; justify-content: center;">
                                         <div style="font-size: 9px; color: var(--text-secondary); font-weight: 600; margin-bottom: 3px;">当前验证码</div>
                                         <div id="token-email" style="font-size: 22px; font-weight: 700; color: var(--primary-color); letter-spacing: 2px; font-family: 'SF Mono', 'Monaco', monospace; padding: 6px 10px; background: white; border-radius: 6px; border: 1px solid var(--border-color); box-shadow: 0 2px 4px rgba(0,0,0,0.08); margin: 0 auto; display: inline-block; min-width: 140px;">生成中...</div>
                                     </div>
                                     
                                     <!-- 倒计时和进度条 -->
                                     <div style="text-align: center; margin-top: 6px;">
                                         <div id="countdown-email" style="font-size: 11px; color: var(--text-secondary); margin-bottom: 4px; font-weight: 600;">剩余时间: --秒</div>
                                         <div style="width: 100%; height: 5px; background-color: rgba(255,255,255,0.8); border-radius: 2px; overflow: hidden; border: 1px solid rgba(0,0,0,0.1);">
                                             <div id="progress-email" style="height: 100%; background: linear-gradient(135deg, var(--success-color), #73d13d); transition: width 1s linear, background-color 0.3s ease; border-radius: 2px;"></div>
                                         </div>
                                     </div>
                                 </div>

                                 <!-- 固定底部操作区域 -->
                                 <div style="background: linear-gradient(135deg, #f8fafc, #edf2f7); border-radius: 6px; border: 1px solid var(--border-color); padding: 8px;">
                                     <div style="text-align: center;">
                                         <button class="btn btn-purple" onclick="copyTOTPEmail()" 
                                                 style="margin: 0; padding: 8px 18px; font-size: 12px; font-weight: 600; box-sizing: border-box; border-radius: 6px; min-width: 120px; box-shadow: 0 2px 6px rgba(114, 46, 209, 0.25);">
                                             📋 复制验证码
                                         </button>
                                         <div id="copy-status-email" style="margin-top: 4px; font-size: 10px; text-align: center; min-height: 12px; font-weight: 600; color: var(--text-secondary);"></div>
                                     </div>
                                 </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：邮件列表和内容 -->
                    <div class="email-right-panel">
                        <!-- 邮件列表 -->
                        <div class="section email-list-section">
                            <h3 style="margin-bottom: 16px;">📬 邮件列表</h3>
                            <div id="emailList" class="scrollable-content" style="height: calc(100% - 50px);">
                                <div style="text-align: center; color: var(--text-secondary); padding: 20px;">
                                    输入邮箱地址并点击"刷新邮件"开始
                                </div>
                            </div>
                        </div>

                        <!-- 邮件详情 -->
                        <div class="section email-detail-section">
                            <h3 style="margin-bottom: 16px;">📄 邮件内容</h3>
                            <div id="emailDetail" class="scrollable-content" style="height: calc(100% - 50px);">
                                <div style="text-align: center; color: var(--text-secondary); padding: 30px;">
                                    点击邮件列表中的邮件查看详细内容
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 驾照生成器标签页 -->
            <div id="license-tab" class="tab-content">
                <!-- 模式切换按钮 -->
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="display: inline-flex; background: var(--bg-card); border: 2px solid var(--border-color); border-radius: 12px; padding: 4px; box-shadow: var(--shadow-light);">
                        <button id="single-mode-btn" class="btn" onclick="switchLicenseMode('single')" style="margin: 0; padding: 8px 16px; font-size: 13px; background: linear-gradient(135deg, var(--primary-color), #40a9ff); border-radius: 8px;">
                            👤 单个生成
                        </button>
                        <button id="batch-mode-btn" class="btn" onclick="switchLicenseMode('batch')" style="margin: 0; padding: 8px 16px; font-size: 13px; background: rgba(0,0,0,0.1); color: var(--text-secondary); border-radius: 8px;">
                            📋 批量生成
                        </button>
                    </div>
                </div>

                <!-- 单个生成模式 -->
                <div id="single-license-section" class="section">
                    <h3>🆔 单个驾照生成器</h3>
                    <p style="margin-bottom: 16px; color: var(--text-secondary);">支持FL、IL、MD、MI、MN、NH、NY、WA、WI九个州</p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label for="single-fname">名 (First Name)</label>
                            <input type="text" id="single-fname" placeholder="例: JOHN" style="text-transform: uppercase;">
                        </div>
                        <div class="form-group">
                            <label for="single-mname">中间名 (Middle Name)</label>
                            <input type="text" id="single-mname" placeholder="例: A (可选)" style="text-transform: uppercase;">
                        </div>
                        <div class="form-group">
                            <label for="single-lname">姓 (Last Name)</label>
                            <input type="text" id="single-lname" placeholder="例: SMITH" style="text-transform: uppercase;">
                        </div>
                        <div class="form-group">
                            <label for="single-birth">出生日期</label>
                            <input type="text" id="single-birth" placeholder="MM/DD/YYYY 例: 05/22/1985">
                        </div>
                        <div class="form-group">
                            <label for="single-state">州代码</label>
                            <select id="single-state">
                                <option value="">选择州</option>
                                <option value="FL">FL - 佛罗里达州</option>
                                <option value="IL">IL - 伊利诺伊州</option>
                                <option value="MD">MD - 马里兰州</option>
                                <option value="MI">MI - 密歇根州</option>
                                <option value="MN">MN - 明尼苏达州</option>
                                <option value="NH">NH - 新罕布什尔州</option>
                                <option value="NY">NY - 纽约州</option>
                                <option value="WA">WA - 华盛顿州</option>
                                <option value="WI">WI - 威斯康星州</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="single-gender">性别</label>
                            <select id="single-gender">
                                <option value="">选择性别</option>
                                <option value="M">M - 男性</option>
                                <option value="F">F - 女性</option>
                            </select>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn btn-success" onclick="generateSingleLicense()" style="padding: 12px 24px; font-size: 14px;">
                            🚀 生成驾照号码
                        </button>
                        <button class="btn btn-purple" onclick="clearSingleForm()" style="padding: 12px 24px; font-size: 14px;">
                            🗑️ 清空表单
                        </button>
                    </div>

                    <div id="single-license-result" style="display: none; background: var(--bg-primary); border: 2px solid var(--success-color); border-radius: 12px; padding: 20px; margin: 20px 0; text-align: center;">
                        <h4 style="color: var(--success-color); margin-bottom: 16px;">✅ 生成成功</h4>
                        <div style="background: white; border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; margin: 12px 0;">
                            <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">驾照号码</div>
                            <div id="single-license-number" style="font-size: 24px; font-weight: 700; color: var(--primary-color); font-family: 'SF Mono', 'Monaco', monospace; letter-spacing: 2px; word-break: break-all;"></div>
                        </div>
                        <div style="margin-top: 16px;">
                            <button class="btn btn-purple" onclick="copySingleLicense()" style="margin: 4px;">
                                📋 复制号码
                            </button>
                            <button class="btn" onclick="generateSingleLicense()" style="margin: 4px; background: linear-gradient(135deg, #52c41a, #73d13d);">
                                🔄 重新生成
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 批量生成模式 -->
                <div id="batch-license-section" class="section" style="display: none;">
                    <h3>🆔 批量驾照生成器</h3>
                    <p style="margin-bottom: 16px; color: var(--text-secondary);">支持FL、IL、MD、MI、MN、NH、NY、WA、WI九个州</p>

                    <div class="form-group">
                        <label for="license-input">批量输入区域</label>
                        <textarea id="license-input" rows="8" placeholder="支持从Excel/表格复制的制表符分隔格式：&#10;FName    中间名    LName    出生日期    州代码    性别&#10;BECCA    M        FERRAIOTTO    06/14/1991    FL    F&#10;JOHN     A        SMITH         05/22/1985    NY    M&#10;MARY              DOE           01/15/1990    WA    F"></textarea>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn" onclick="previewData()" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);">👁️ 预览数据</button>
                        <button class="btn btn-success" onclick="batchExtractLicense()">🚀 批量生成</button>
                        <button class="btn btn-warning" onclick="exportResults()" id="exportBtn" style="display: none;">📊 导出CSV</button>
                        <button class="btn btn-purple" onclick="copyAllLicenses()" id="copyAllBtn" style="display: none;">📋 复制所有</button>
                    </div>

                    <div id="license-copy-status" style="text-align: center; margin: 10px 0; font-size: 14px; min-height: 20px;"></div>
                    
                    <div id="license-loading" class="status info" style="display: none;">
                        <span class="loading"></span>正在批量处理中，请稍候...
                    </div>
                    
                    <div id="license-result" style="display: none;">
                        <h4 style="margin-bottom: 15px; color: var(--text-primary);">生成结果</h4>
                        <div id="license-table-container" style="background: var(--bg-card); border: 2px solid var(--border-color); border-radius: 8px; overflow: auto; max-height: 400px;">
                            <table id="license-table" style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8fafc;">
                                        <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">名</th>
                                        <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">中间名</th>
                                        <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">姓</th>
                                        <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">出生日期</th>
                                        <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">州</th>
                                        <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">性别</th>
                                        <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">驾照号码</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入TOTP库 -->
    <script src="https://cdn.jsdelivr.net/npm/otpauth@latest/dist/otpauth.umd.min.js"></script>

    <script>
        // ==================== 全局变量和配置 ====================

        // 邮箱API配置
        const CONFIG = {
            apiUrl: 'https://api-1721131107.cuiqiu.vip',
            token: '6eed041271224e119cf650e45fb44692',
        };

        let currentMailboxId = null;

        // ==================== 标签页切换功能 ====================

        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有标签按钮的active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');

            // 激活对应的标签按钮
            event.target.classList.add('active');
        }

        // ==================== 邮箱查看器功能 ====================

        // 显示邮箱状态
        function showEmailStatus(message, type = 'info', showLoading = false) {
            const statusDiv = document.getElementById('email-status');
            statusDiv.className = `status ${type}`;

            if (showLoading) {
                statusDiv.innerHTML = `<span class="loading"></span>${message}`;
            } else {
                statusDiv.textContent = message;
            }
        }

        // API请求
        async function apiRequest(endpoint, data = {}) {
            const formData = new FormData();
            formData.append('token', CONFIG.token);

            for (const key in data) {
                if (data[key] !== null && data[key] !== undefined) {
                    if (Array.isArray(data[key])) {
                        data[key].forEach(item => formData.append(key + '[]', item));
                    } else {
                        formData.append(key, data[key]);
                    }
                }
            }

            const response = await fetch(CONFIG.apiUrl + endpoint, {
                method: 'POST',
                body: formData
            });

            return await response.json();
        }

        // 根据邮箱地址查找代收邮箱ID
        async function findReceiveMailbox(emailAddress) {
            try {
                const domain = emailAddress.split('@')[1];
                if (!domain) {
                    throw new Error('邮箱地址格式不正确');
                }

                showEmailStatus(`正在查找 ${emailAddress} 的代收邮箱...`, 'info', true);

                const domainResult = await apiRequest('/v1/domain/list', { page: 1, limit: 50 });

                if (domainResult.code !== 200 && domainResult.code !== 0) {
                    throw new Error(`获取域名列表失败: ${domainResult.msg}`);
                }

                let domains = [];
                if (domainResult.data && domainResult.data.list) {
                    domains = domainResult.data.list;
                }

                const defaultDomain = domains.find(d => d.domain === 'yahoogbd.com');
                if (!defaultDomain) {
                    throw new Error('未找到代收域名 yahoogbd.com');
                }

                const mailResult = await apiRequest('/v1/mail/list', {
                    domain_id: defaultDomain.id,
                    page: 1,
                    limit: 50
                });

                if (mailResult.code !== 200 && mailResult.code !== 0) {
                    throw new Error(`获取邮箱列表失败: ${mailResult.msg}`);
                }

                let mailboxes = [];
                if (mailResult.data && mailResult.data.list) {
                    mailboxes = mailResult.data.list;
                }

                if (mailboxes.length === 0) {
                    throw new Error('yahoogbd.com 域名下没有邮箱');
                }

                const receiveMailbox = mailboxes[0];
                return receiveMailbox.id;

            } catch (error) {
                throw new Error(`查找代收邮箱失败: ${error.message}`);
            }
        }

        // 辅助函数：提取邮箱地址字符串
        function extractEmailString(field) {
            if (!field) return '';
            if (typeof field === 'string') return field;
            if (typeof field === 'object') {
                if (field.email) return field.email;
                if (field.address) return field.address;
                if (field.mail) return field.mail;
                if (Array.isArray(field)) {
                    return field.map(item => extractEmailString(item)).join(' ');
                }
                return JSON.stringify(field);
            }
            return String(field);
        }

        // 显示邮件列表
        function displayEmails(emails, emailAddress) {
            const emailListDiv = document.getElementById('emailList');

            if (emails.length === 0) {
                emailListDiv.innerHTML = `
                    <div style="padding: 30px; text-align: center; color: var(--text-secondary);">
                        📭 <strong>${emailAddress}</strong> 在指定时间范围内暂无邮件<br>
                        <small style="color: var(--text-secondary); margin-top: 10px; display: block;">💡 尝试扩大时间范围或稍后再试</small>
                    </div>
                `;
                showEmailStatus(`暂无新邮件`, 'info');
                return;
            }

            emailListDiv.innerHTML = '';
            emails.forEach((email, index) => {
                const emailItem = document.createElement('div');
                emailItem.className = 'email-item';
                emailItem.onclick = () => showEmailDetail(email.id);

                emailItem.innerHTML = `
                    <div class="email-subject">${email.subject || '(无主题)'}</div>
                    <div class="email-meta">
                        <strong>发件人:</strong> ${email.from || '未知'}<br>
                        <strong>收件人:</strong> ${extractEmailString(email.to) || '未知'}<br>
                        <strong>时间:</strong> ${email.time || '未知'}
                    </div>
                `;

                emailListDiv.appendChild(emailItem);
            });

            showEmailStatus(`✅ 找到 ${emails.length} 封发送给 ${emailAddress} 的邮件`, 'success');
        }

        // 显示全部邮件列表
        function displayAllEmails(emails) {
            const emailListDiv = document.getElementById('emailList');

            if (emails.length === 0) {
                emailListDiv.innerHTML = `
                    <div style="padding: 30px; text-align: center; color: var(--text-secondary);">
                        📭 在指定时间范围内暂无邮件<br>
                        <small style="color: var(--text-secondary); margin-top: 10px; display: block;">💡 尝试扩大时间范围或稍后再试</small>
                    </div>
                `;
                showEmailStatus(`暂无邮件`, 'info');
                return;
            }

            emailListDiv.innerHTML = '';
            emails.forEach((email, index) => {
                const emailItem = document.createElement('div');
                emailItem.className = 'email-item';
                emailItem.onclick = () => showEmailDetail(email.id);

                emailItem.innerHTML = `
                    <div class="email-subject">${email.subject || '(无主题)'}</div>
                    <div class="email-meta">
                        <strong>发件人:</strong> ${email.from || '未知'}<br>
                        <strong>收件人:</strong> ${extractEmailString(email.to) || '未知'}<br>
                        <strong>时间:</strong> ${email.time || '未知'}
                    </div>
                `;

                emailListDiv.appendChild(emailItem);
            });

            showEmailStatus(`✅ 找到 ${emails.length} 封邮件（全部邮箱）`, 'success');
        }

        // 获取时间范围
        function getTimeRange() {
            const minutes = parseInt(document.getElementById('timeRange').value);
            const endTime = new Date();
            const startTime = new Date(endTime.getTime() - minutes * 60 * 1000);

            const formatDate = (date) => {
                return date.getFullYear() + '-' +
                       String(date.getMonth() + 1).padStart(2, '0') + '-' +
                       String(date.getDate()).padStart(2, '0');
            };

            return {
                start_time: formatDate(startTime),
                end_time: formatDate(new Date(endTime.getTime() + 24 * 60 * 60 * 1000))
            };
        }

        // 查看邮件
        async function checkEmails() {
            const emailAddress = document.getElementById('emailAddress').value.trim();

            if (!emailAddress) {
                showEmailStatus('请输入邮箱地址', 'error');
                return;
            }

            if (!emailAddress.includes('@')) {
                showEmailStatus('请输入有效的邮箱地址', 'error');
                return;
            }

            try {
                document.getElementById('emailList').innerHTML = '<div style="padding: 30px; text-align: center; color: var(--text-secondary);">正在加载...</div>';
                document.getElementById('emailDetail').innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 30px;">正在加载...</div>';

                showEmailStatus('正在查找邮箱...', 'info', true);
                currentMailboxId = await findReceiveMailbox(emailAddress);

                showEmailStatus('正在获取邮件...', 'info', true);
                const timeRange = getTimeRange();

                const result = await apiRequest('/v1/message/list', {
                    mail_id: currentMailboxId,
                    folder: ['Inbox'],
                    start_time: timeRange.start_time,
                    end_time: timeRange.end_time,
                    page: 1,
                    limit: 100,
                    to: emailAddress
                });

                if (result.code !== 200 && result.code !== 0) {
                    throw new Error(result.msg || '获取邮件失败');
                }

                const emails = result.data && result.data.list ? result.data.list : [];
                displayEmails(emails, emailAddress);

                if (emails.length > 0) {
                    setTimeout(() => {
                        showEmailDetail(emails[0].id);
                    }, 500);
                }

            } catch (error) {
                showEmailStatus(`❌ ${error.message}`, 'error');
                document.getElementById('emailList').innerHTML = `
                    <div style="padding: 30px; text-align: center; color: var(--error-color);">
                        ❌ ${error.message}
                    </div>
                `;
            }
        }

        // 接收全部邮件
        async function checkAllEmails() {
            try {
                document.getElementById('emailList').innerHTML = '<div style="padding: 30px; text-align: center; color: var(--text-secondary);">正在加载全部邮件...</div>';
                document.getElementById('emailDetail').innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 30px;">正在加载...</div>';

                showEmailStatus('正在获取代收邮箱...', 'info', true);

                // 获取默认代收邮箱
                const domainResult = await apiRequest('/v1/domain/list', { page: 1, limit: 50 });

                if (domainResult.code !== 200 && domainResult.code !== 0) {
                    throw new Error(`获取域名列表失败: ${domainResult.msg}`);
                }

                let domains = [];
                if (domainResult.data && domainResult.data.list) {
                    domains = domainResult.data.list;
                }

                const defaultDomain = domains.find(d => d.domain === 'yahoogbd.com');
                if (!defaultDomain) {
                    throw new Error('未找到代收域名 yahoogbd.com');
                }

                const mailResult = await apiRequest('/v1/mail/list', {
                    domain_id: defaultDomain.id,
                    page: 1,
                    limit: 50
                });

                if (mailResult.code !== 200 && mailResult.code !== 0) {
                    throw new Error(`获取邮箱列表失败: ${mailResult.msg}`);
                }

                let mailboxes = [];
                if (mailResult.data && mailResult.data.list) {
                    mailboxes = mailResult.data.list;
                }

                if (mailboxes.length === 0) {
                    throw new Error('yahoogbd.com 域名下没有邮箱');
                }

                const receiveMailbox = mailboxes[0];
                currentMailboxId = receiveMailbox.id;

                showEmailStatus('正在获取全部邮件...', 'info', true);
                const timeRange = getTimeRange();

                // 获取全部邮件，不指定特定收件人
                const result = await apiRequest('/v1/message/list', {
                    mail_id: currentMailboxId,
                    folder: ['Inbox'],
                    start_time: timeRange.start_time,
                    end_time: timeRange.end_time,
                    page: 1,
                    limit: 100
                    // 注意：这里不传 to 参数，获取全部邮件
                });

                if (result.code !== 200 && result.code !== 0) {
                    throw new Error(result.msg || '获取邮件失败');
                }

                const emails = result.data && result.data.list ? result.data.list : [];
                displayAllEmails(emails);

                if (emails.length > 0) {
                    setTimeout(() => {
                        showEmailDetail(emails[0].id);
                    }, 500);
                }

            } catch (error) {
                showEmailStatus(`❌ ${error.message}`, 'error');
                document.getElementById('emailList').innerHTML = `
                    <div style="padding: 30px; text-align: center; color: var(--error-color);">
                        ❌ ${error.message}
                    </div>
                `;
            }
        }

        // 显示邮件详情
        async function showEmailDetail(messageId) {
            if (!currentMailboxId) {
                showEmailStatus('请先查看邮件列表', 'error');
                return;
            }

            try {
                showEmailStatus('正在加载邮件详情...', 'info', true);

                const result = await apiRequest('/v1/message/detail', {
                    mail_id: currentMailboxId,
                    message_id: messageId,
                    folder: 'Inbox'
                });

                if (result.code !== 200 && result.code !== 0) {
                    throw new Error(result.msg || '获取邮件详情失败');
                }

                let email = null;
                if (result.data && result.data.list && result.data.list.length > 0) {
                    email = result.data.list[0];
                } else if (result.data && typeof result.data === 'object' && !Array.isArray(result.data)) {
                    email = result.data;
                } else if (Array.isArray(result.data) && result.data.length > 0) {
                    email = result.data[0];
                }

                if (!email) {
                    throw new Error('邮件详情为空');
                }

                if (email.content && typeof email.content === 'object') {
                    email = { ...email, ...email.content };
                }

                const detailDiv = document.getElementById('emailDetail');
                detailDiv.innerHTML = `
                    <div style="border-bottom: 2px solid var(--border-color); padding-bottom: 16px; margin-bottom: 16px;">
                        <h4 style="color: var(--text-primary); margin-bottom: 12px; font-size: 1.1em;">${email.subject || '(无主题)'}</h4>
                        <div style="color: var(--text-secondary); font-size: 14px; line-height: 1.6;">
                            <p style="margin: 4px 0;"><strong>发件人:</strong> ${email.from || '未知'}</p>
                            <p style="margin: 4px 0;"><strong>收件人:</strong> ${extractEmailString(email.to) || '未知'}</p>
                            <p style="margin: 4px 0;"><strong>时间:</strong> ${email.time || '未知'}</p>
                            ${email.attachments ? `<p style="margin: 4px 0;"><strong>附件:</strong> ${email.attachments}</p>` : ''}
                        </div>
                    </div>
                    <div style="line-height: 1.7;">
                        <h5 style="color: var(--text-primary); margin-bottom: 12px; font-size: 1em;">邮件内容:</h5>
                        <div style="background: var(--bg-primary); padding: 16px; border-radius: 8px; border-left: 4px solid var(--primary-color); color: var(--text-primary);">
                            ${email.body || email.plain_text || '邮件内容为空'}
                        </div>
                    </div>
                `;

                showEmailStatus('邮件详情加载完成', 'success');

            } catch (error) {
                showEmailStatus(`获取邮件详情失败: ${error.message}`, 'error');
                document.getElementById('emailDetail').innerHTML = `
                    <div style="text-align: center; color: var(--error-color); padding: 30px;">
                        ❌ 加载邮件详情失败: ${error.message}
                    </div>
                `;
            }
        }

        // ==================== 邮箱页面TOTP功能 ====================

        let totpEmail = null;

        // 初始化邮箱页面TOTP
        function initTOTPEmail(secret) {
            totpEmail = new OTPAuth.TOTP({
                algorithm: "SHA1",
                digits: 6,
                period: 30,
                secret: secret
            });
        }

        // 更新邮箱页面TOTP显示
        function updateTOTPDisplayEmail() {
            try {
                if (!totpEmail) {
                    document.getElementById('token-email').textContent = '请输入密钥';
                    document.getElementById('countdown-email').textContent = '等待密钥输入...';
                    return;
                }

                const token = totpEmail.generate();
                const remaining = totpEmail.remaining();
                const remainingSeconds = Math.floor(remaining / 1000);
                const progress = ((30 - remainingSeconds) / 30) * 100;

                document.getElementById('token-email').textContent = token;
                document.getElementById('countdown-email').textContent = `剩余时间: ${remainingSeconds}秒`;

                const progressBar = document.getElementById('progress-email');
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;

                    if (remainingSeconds <= 5) {
                        progressBar.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
                    } else if (remainingSeconds <= 10) {
                        progressBar.style.background = 'linear-gradient(135deg, #ff9800, #f57c00)';
                    } else {
                        progressBar.style.background = 'linear-gradient(135deg, var(--success-color), #73d13d)';
                    }
                }

            } catch (error) {
                console.error('生成验证码失败:', error);
                document.getElementById('token-email').textContent = '错误';
            }
        }

        // 更新邮箱页面密钥函数
        function updateTOTPSecretEmail() {
            const inputValue = document.getElementById('secret-input-email').value;
            const statusDiv = document.getElementById('secret-status-email');

            const cleanSecret = inputValue
                .replace(/\s+/g, '')
                .replace(/[^A-Z2-7]/gi, '')
                .toUpperCase();

            const secretInput = document.getElementById('secret-input-email');
            if (secretInput.value !== cleanSecret) {
                secretInput.value = cleanSecret;
            }

            if (cleanSecret.length < 8) {
                statusDiv.innerHTML = '<span style="color: orange;">⏳ 密钥太短，请继续输入...</span>';
                return;
            }

            try {
                initTOTPEmail(cleanSecret);
                statusDiv.innerHTML = '<span style="color: var(--success-color);">✅ 密钥更新成功</span>';
                updateTOTPDisplayEmail();
                localStorage.setItem('totp-secret-email', cleanSecret);
            } catch (error) {
                statusDiv.innerHTML = '<span style="color: var(--error-color);">❌ 密钥格式错误</span>';
            }
        }

        // 显示邮箱页面复制状态
        function showCopyStatusEmail(message, type = 'success') {
            const statusDiv = document.getElementById('copy-status-email');
            if (statusDiv) {
                const colorMap = {
                    success: 'var(--success-color)',
                    error: 'var(--error-color)',
                    warning: 'var(--warning-color)'
                };
                statusDiv.innerHTML = `<span style="color: ${colorMap[type] || colorMap.success};">${message}</span>`;
                // 3秒后清除状态
                setTimeout(() => {
                    statusDiv.innerHTML = '';
                }, 3000);
            }
        }

        // 复制邮箱页面TOTP验证码
        function copyTOTPEmail() {
            const token = document.getElementById('token-email').textContent;
            if (token && token !== '请输入密钥' && token !== '错误' && token !== '生成中...') {
                navigator.clipboard.writeText(token).then(() => {
                    showCopyStatusEmail('✅ 验证码已复制: ' + token);
                }).catch(() => {
                    showCopyStatusEmail('❌ 复制失败，请手动复制', 'error');
                });
            } else {
                showCopyStatusEmail('⚠️ 请先输入有效的TOTP密钥', 'warning');
            }
        }

        // ==================== 驾照生成器功能 ====================

        // 当前模式：single 或 batch
        let currentLicenseMode = 'single';

        // 模式切换功能
        function switchLicenseMode(mode) {
            currentLicenseMode = mode;

            const singleBtn = document.getElementById('single-mode-btn');
            const batchBtn = document.getElementById('batch-mode-btn');
            const singleSection = document.getElementById('single-license-section');
            const batchSection = document.getElementById('batch-license-section');

            if (mode === 'single') {
                // 激活单个模式
                singleBtn.style.background = 'linear-gradient(135deg, var(--primary-color), #40a9ff)';
                singleBtn.style.color = 'white';
                batchBtn.style.background = 'rgba(0,0,0,0.1)';
                batchBtn.style.color = 'var(--text-secondary)';

                singleSection.style.display = 'block';
                batchSection.style.display = 'none';
            } else {
                // 激活批量模式
                batchBtn.style.background = 'linear-gradient(135deg, var(--primary-color), #40a9ff)';
                batchBtn.style.color = 'white';
                singleBtn.style.background = 'rgba(0,0,0,0.1)';
                singleBtn.style.color = 'var(--text-secondary)';

                singleSection.style.display = 'none';
                batchSection.style.display = 'block';
            }
        }

        // 单个驾照生成功能
        function generateSingleLicense() {
            // 获取表单数据
            const firstName = document.getElementById('single-fname').value.trim().toUpperCase();
            const middleName = document.getElementById('single-mname').value.trim().toUpperCase();
            const lastName = document.getElementById('single-lname').value.trim().toUpperCase();
            const birthDate = document.getElementById('single-birth').value.trim();
            const state = document.getElementById('single-state').value;
            const gender = document.getElementById('single-gender').value;

            // 验证必填字段
            if (!firstName) {
                showLicenseCopyStatus('❌ 请输入名字', 'error');
                document.getElementById('single-fname').focus();
                return;
            }

            if (!lastName) {
                showLicenseCopyStatus('❌ 请输入姓氏', 'error');
                document.getElementById('single-lname').focus();
                return;
            }

            if (!birthDate) {
                showLicenseCopyStatus('❌ 请输入出生日期', 'error');
                document.getElementById('single-birth').focus();
                return;
            }

            if (!state) {
                showLicenseCopyStatus('❌ 请选择州', 'error');
                document.getElementById('single-state').focus();
                return;
            }

            if (!gender) {
                showLicenseCopyStatus('❌ 请选择性别', 'error');
                document.getElementById('single-gender').focus();
                return;
            }

            // 验证出生日期格式
            const datePattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
            const dateMatch = birthDate.match(datePattern);
            if (!dateMatch) {
                showLicenseCopyStatus('❌ 出生日期格式错误，请使用 MM/DD/YYYY 格式', 'error');
                document.getElementById('single-birth').focus();
                return;
            }

            const [, month, day, year] = dateMatch;

            // 验证日期有效性
            const monthNum = parseInt(month);
            const dayNum = parseInt(day);
            const yearNum = parseInt(year);

            if (monthNum < 1 || monthNum > 12) {
                showLicenseCopyStatus('❌ 月份必须在1-12之间', 'error');
                return;
            }

            if (dayNum < 1 || dayNum > 31) {
                showLicenseCopyStatus('❌ 日期必须在1-31之间', 'error');
                return;
            }

            if (yearNum < 1900 || yearNum > new Date().getFullYear()) {
                showLicenseCopyStatus('❌ 年份不合理', 'error');
                return;
            }

            try {
                // 生成驾照号码
                const licenseNumber = generateLicenseNumber(firstName, middleName, lastName, month, day, year, gender, state);

                // 显示结果
                document.getElementById('single-license-number').textContent = licenseNumber;
                document.getElementById('single-license-result').style.display = 'block';

                // 滚动到结果区域
                document.getElementById('single-license-result').scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                showLicenseCopyStatus('✅ 驾照号码生成成功！', 'success');

            } catch (error) {
                showLicenseCopyStatus(`❌ 生成失败: ${error.message}`, 'error');
            }
        }

        // 复制单个驾照号码
        function copySingleLicense() {
            const licenseNumber = document.getElementById('single-license-number').textContent;
            if (licenseNumber) {
                navigator.clipboard.writeText(licenseNumber).then(() => {
                    showLicenseCopyStatus('✅ 驾照号码已复制到剪贴板', 'success');
                }).catch(() => {
                    showLicenseCopyStatus('❌ 复制失败，请手动复制', 'error');
                });
            }
        }

        // 清空单个生成表单
        function clearSingleForm() {
            document.getElementById('single-fname').value = '';
            document.getElementById('single-mname').value = '';
            document.getElementById('single-lname').value = '';
            document.getElementById('single-birth').value = '';
            document.getElementById('single-state').value = '';
            document.getElementById('single-gender').value = '';
            document.getElementById('single-license-result').style.display = 'none';
            showLicenseCopyStatus('🗑️ 表单已清空', 'success');
        }

        // 生成驾照号码的核心函数
        function generateLicenseNumber(firstName, middleName, lastName, month, day, year, gender, state) {
            if (!stateConfig[state]) {
                throw new Error(`不支持的州代码: ${state}`);
            }

            return stateConfig[state].generate(firstName, middleName, lastName, month, day, year, gender);
        }

        // 州名映射表
        const stateNameMap = {
            'FLORIDA': 'FL', 'ILLINOIS': 'IL', 'MARYLAND': 'MD', 'MICHIGAN': 'MI', 'MINNESOTA': 'MN',
            'NEWHAMPSHIRE': 'NH', 'NEW HAMPSHIRE': 'NH', 'NEWYORK': 'NY', 'NEW YORK': 'NY',
            'WASHINGTON': 'WA', 'WISCONSIN': 'WI',
            'FL': 'FL', 'IL': 'IL', 'MD': 'MD', 'MI': 'MI', 'MN': 'MN',
            'NH': 'NH', 'NY': 'NY', 'WA': 'WA', 'WI': 'WI'
        };

        // 性别映射表
        const genderMap = {
            'F': 'F', 'FEMALE': 'F', 'WOMAN': 'F', 'WOMEN': 'F',
            'M': 'M', 'MALE': 'M', 'MAN': 'M', 'MEN': 'M'
        };

        // 州配置和算法
        const stateConfig = {
            'FL': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const soundex = calculateSoundex(last);
                    const nameCode = calculateNameCode(first, middle);
                    const yearCode = y.slice(-2);
                    const dateGenderCode = calculateDateGenderCode(m, d, gender, 40, 500);
                    const overflow = Math.floor(Math.random() * 10);
                    return `${soundex}-${nameCode}-${yearCode}-${dateGenderCode}-${overflow}`;
                }
            },
            'IL': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const soundex = calculateSoundex(last);
                    const nameCode = calculateNameCode(first, middle);
                    const yearCode = y.slice(-2);
                    const dateGenderCode = calculateDateGenderCode(m, d, gender, 31, 600);
                    // Illinois格式: SSSS-FFFY-YDDD
                    return `${soundex}-${nameCode}${yearCode.charAt(0)}-${yearCode.charAt(1)}${dateGenderCode}`;
                }
            },
            'WI': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const soundex = calculateSoundex(last);
                    const nameCode = calculateNameCode(first, middle);
                    const yearCode = y.slice(-2);
                    const dateGenderCode = calculateDateGenderCode(m, d, gender, 40, 500);
                    const overflow = Math.floor(Math.random() * 100).toString().padStart(2, '0');
                    return `${soundex}-${nameCode}${yearCode.charAt(0)}-${yearCode.charAt(1)}${dateGenderCode}-${overflow}`;
                }
            },
            'NY': {
                generate: (first, middle, last, m, d, y, gender) => {
                    return Math.random().toString().slice(2, 11);
                }
            },
            'WA': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const lastPart = last.slice(0, 5).toUpperCase().padEnd(5, 'X');
                    const firstInitial = first.charAt(0).toUpperCase();
                    const middleInitial = middle ? middle.charAt(0).toUpperCase() : 'X';
                    const numbers = Math.random().toString().slice(2, 5);
                    const randomLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('').sort(() => 0.5 - Math.random()).slice(0, 2).join('');
                    return `${lastPart}${firstInitial}${middleInitial}${numbers}${randomLetters}`;
                }
            },
            'MD': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const firstInitial = first.charAt(0).toUpperCase();
                    const randomNum = Math.random().toString().slice(2, 14).padStart(12, '0');
                    return `${firstInitial}${randomNum}`;
                }
            },
            'MI': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const firstInitial = first.charAt(0).toUpperCase();
                    const randomNum = Math.random().toString().slice(2, 14).padStart(12, '0');
                    return `${firstInitial}${randomNum}`;
                }
            },
            'MN': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const firstInitial = first.charAt(0).toUpperCase();
                    const randomNum = Math.random().toString().slice(2, 14).padStart(12, '0');
                    return `${firstInitial}${randomNum}`;
                }
            },
            'NH': {
                generate: (first, middle, last, m, d, y, gender) => {
                    const mm = m.padStart(2, '0');
                    const lastInitials = last.slice(0, 2).toUpperCase();
                    const firstInitial = first.charAt(0).toUpperCase();
                    const dd = d.padStart(2, '0');
                    const overflow = Math.floor(Math.random() * 10);
                    return `${mm}${lastInitials}${firstInitial}${y.slice(-2)}${dd}${overflow}`;
                }
            }
        };

        // Soundex算法实现
        function calculateSoundex(str) {
            if (!str) return 'A000';

            str = str.toUpperCase();
            let code = str[0];

            const soundexMap = {
                'BFPV': '1', 'CGJKQSXZ': '2', 'DT': '3', 'L': '4', 'MN': '5', 'R': '6'
            };

            let previous = '0';
            for (let i = 1; i < str.length; i++) {
                let current = '0';
                for (let key in soundexMap) {
                    if (key.includes(str[i])) {
                        current = soundexMap[key];
                        break;
                    }
                }
                if (current !== '0' && current !== previous) {
                    code += current;
                }
                previous = current;
            }

            return (code + '000').slice(0, 4);
        }

        // 名字编码表
        const nameCodeTable = {
            'ALBERT': '20', 'ALICE': '20', 'ANN': '40', 'ANNA': '40', 'ANNE': '40',
            'ANNIE': '40', 'ARTHUR': '40', 'BERNARD': '80', 'BETTE': '80', 'BETTIE': '80',
            'BETTY': '80', 'CARL': '120', 'CATHERINE': '120', 'CHARLES': '140', 'DORTHY': '180',
            'EDWARD': '220', 'ELIZABETH': '220', 'FLORENCE': '260', 'DONALD': '180', 'CLARA': '140',
            'FRANK': '260', 'GEORGE': '300', 'GRACE': '300', 'HAROLD': '340', 'HARRIET': '340',
            'HARRY': '360', 'HAZEL': '360', 'HELEN': '380', 'HENRY': '380', 'JAMES': '440',
            'JANE': '440', 'JAYNE': '440', 'JEAN': '460', 'JOAN': '480', 'JOHN': '460',
            'JOSEPH': '480', 'MARGARET': '560', 'MARTIN': '560', 'MARVIN': '580', 'MARY': '580',
            'MELVIN': '600', 'MILDRED': '600', 'PATRICIA': '680', 'PAUL': '680', 'RICHARD': '740',
            'ROBERT': '760', 'RUBY': '740', 'RUTH': '760', 'THELMA': '820', 'THOMAS': '820',
            'WALTER': '900', 'WANDA': '900', 'WILLIAM': '920', 'WILMA': '920'
        };

        // 首字母编码表
        const initialCodeTable = {
            'A': '0', 'B': '60', 'C': '100', 'D': '160', 'E': '200',
            'F': '240', 'G': '280', 'H': '320', 'I': '400', 'J': '420',
            'K': '500', 'L': '520', 'M': '540', 'N': '620', 'O': '640',
            'P': '660', 'Q': '700', 'R': '720', 'S': '780', 'T': '800',
            'U': '840', 'V': '860', 'W': '880', 'X': '940', 'Y': '960', 'Z': '980'
        };

        // 中间名编码表
        const middleInitialCodeTable = {
            'A': '1', 'B': '2', 'C': '3', 'D': '4', 'E': '5', 'F': '6', 'G': '7', 'H': '8', 'I': '9', 'J': '10',
            'K': '11', 'L': '12', 'M': '13', 'N': '14', 'O': '14', 'P': '15', 'Q': '15', 'R': '16', 'S': '17', 'T': '18',
            'U': '18', 'V': '18', 'W': '19', 'X': '19', 'Y': '19', 'Z': '19'
        };

        // 计算名字和中间名编码
        function calculateNameCode(firstName, middleInitial) {
            let nameCode = 0;

            const upperFirstName = firstName.toUpperCase();
            if (nameCodeTable[upperFirstName]) {
                nameCode = parseInt(nameCodeTable[upperFirstName]);
            } else {
                const firstInitial = upperFirstName.charAt(0);
                if (initialCodeTable[firstInitial]) {
                    nameCode = parseInt(initialCodeTable[firstInitial]);
                }
            }

            if (middleInitial && middleInitial.trim() !== '') {
                const upperMiddleInitial = middleInitial.toUpperCase();
                if (middleInitialCodeTable[upperMiddleInitial]) {
                    nameCode += parseInt(middleInitialCodeTable[upperMiddleInitial]);
                }
            }

            return nameCode.toString().padStart(3, '0');
        }

        // 计算日期和性别编码
        function calculateDateGenderCode(month, day, gender, monthMultiplier, genderMod) {
            const m = parseInt(month);
            const d = parseInt(day);
            const genderValue = gender.toUpperCase() === 'F' ? genderMod : 0;

            let code = ((m - 1) * monthMultiplier) + d + genderValue;
            return code.toString().padStart(3, '0');
        }

        // 预览数据功能
        function previewData() {
            const input = document.getElementById('license-input').value;
            if (!input.trim()) {
                showLicenseCopyStatus('⚠️ 请先输入数据', 'warning');
                return;
            }

            const lines = input.trim().split('\n');
            const startIndex = lines[0].includes('FName') || lines[0].includes('姓') ? 1 : 0;

            let previewHtml = '<div style="background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: 8px; padding: 16px; margin: 12px 0; max-height: 300px; overflow-y: auto;">';
            previewHtml += '<h4 style="margin-top: 0; color: var(--text-primary);">📋 数据预览</h4>';
            previewHtml += '<table style="width: 100%; border-collapse: collapse; font-size: 12px;">';
            previewHtml += '<thead><tr style="background: #f8fafc;">';
            previewHtml += '<th style="padding: 8px; border: 1px solid var(--border-color);">名</th>';
            previewHtml += '<th style="padding: 8px; border: 1px solid var(--border-color);">中间名</th>';
            previewHtml += '<th style="padding: 8px; border: 1px solid var(--border-color);">姓</th>';
            previewHtml += '<th style="padding: 8px; border: 1px solid var(--border-color);">出生日期</th>';
            previewHtml += '<th style="padding: 8px; border: 1px solid var(--border-color);">州</th>';
            previewHtml += '<th style="padding: 8px; border: 1px solid var(--border-color);">性别</th>';
            previewHtml += '</tr></thead><tbody>';

            let validCount = 0;
            let errorCount = 0;

            for (let i = startIndex; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                try {
                    const result = parsePersonData(line);
                    if (result.success) {
                        validCount++;
                        previewHtml += '<tr style="background: #f6ffed;">';
                        previewHtml += `<td style="padding: 6px; border: 1px solid var(--border-color);">${result.data.first}</td>`;
                        previewHtml += `<td style="padding: 6px; border: 1px solid var(--border-color);">${result.data.middle}</td>`;
                        previewHtml += `<td style="padding: 6px; border: 1px solid var(--border-color);">${result.data.last}</td>`;
                        previewHtml += `<td style="padding: 6px; border: 1px solid var(--border-color);">${result.data.dob}</td>`;
                        previewHtml += `<td style="padding: 6px; border: 1px solid var(--border-color);">${result.data.state}</td>`;
                        previewHtml += `<td style="padding: 6px; border: 1px solid var(--border-color);">${result.data.gender}</td>`;
                        previewHtml += '</tr>';
                    } else {
                        errorCount++;
                        previewHtml += '<tr style="background: #fff2f0;">';
                        previewHtml += `<td colspan="6" style="padding: 6px; border: 1px solid var(--border-color); color: var(--error-color);">❌ 第${i+1}行错误: ${result.error}</td>`;
                        previewHtml += '</tr>';
                    }
                } catch (error) {
                    errorCount++;
                    previewHtml += '<tr style="background: #fff2f0;">';
                    previewHtml += `<td colspan="6" style="padding: 6px; border: 1px solid var(--border-color); color: var(--error-color);">❌ 第${i+1}行解析失败</td>`;
                    previewHtml += '</tr>';
                }
            }

            previewHtml += '</tbody></table>';
            previewHtml += `<div style="margin-top: 12px; font-size: 14px;">`;
            previewHtml += `✅ 有效数据: ${validCount} 条 | ❌ 错误数据: ${errorCount} 条`;
            previewHtml += `</div></div>`;

            // 显示预览结果
            const resultDiv = document.getElementById('license-result');
            resultDiv.innerHTML = previewHtml;
            resultDiv.style.display = 'block';
        }

        // 解析单行人员数据
        function parsePersonData(line) {
            try {
                // 先处理制表符，保留空字段
                let processedLine = line.replace(/\t/g, '|TAB|'); // 用特殊标记替换制表符

                // 州名映射处理
                processedLine = processedLine.toUpperCase();
                for (const [fullName, abbr] of Object.entries(stateNameMap)) {
                    processedLine = processedLine.replace(new RegExp(fullName, 'g'), abbr);
                }

                // 标准化空格但保留制表符标记
                processedLine = processedLine.replace(/\s+/g, ' ');

                // 分割字段，保留空字段
                let fields = processedLine.split('|TAB|').map(field => field.trim());

                if (fields.length < 4) {
                    return { success: false, error: '字段数量不足，至少需要：名字、姓氏、出生日期、州代码' };
                }

                // 查找日期字段
                let dateIndex = -1;
                for (let j = 0; j < fields.length; j++) {
                    if (fields[j].match(/\d{2}[\/\-]\d{2}[\/\-]\d{4}/)) {
                        dateIndex = j;
                        break;
                    }
                }

                if (dateIndex === -1) {
                    return { success: false, error: '未找到有效的日期格式（应为MM/DD/YYYY）' };
                }

                let first, middle, last, dob, state, gender;

                if (dateIndex === 2) {
                    // 格式：名 姓 日期 州 性别 (无中间名)
                    first = fields[0];
                    last = fields[1];
                    middle = '';
                } else if (dateIndex === 3) {
                    // 格式：名 中间名 姓 日期 州 性别 (有中间名，可能为空)
                    first = fields[0];
                    middle = fields[1] ? fields[1].charAt(0) : ''; // 中间名可能为空
                    last = fields[2];
                } else if (dateIndex === 4) {
                    // 格式：名 中间名1 中间名2 姓 日期 州 性别 (多个中间名，取第一个)
                    first = fields[0];
                    middle = fields[1] ? fields[1].charAt(0) : '';
                    last = fields[3];
                }

                dob = fields[dateIndex];
                state = fields[dateIndex + 1] || 'NY';
                gender = genderMap[fields[dateIndex + 2]] || 'M';

                return {
                    success: true,
                    data: { first, middle, last, dob, state, gender }
                };
            } catch (error) {
                return { success: false, error: '数据解析失败' };
            }
        }

        // 批量处理驾照生成
        async function batchExtractLicense() {
            const input = document.getElementById('license-input').value;
            const loadingDiv = document.getElementById('license-loading');
            const resultDiv = document.getElementById('license-result');
            const exportBtn = document.getElementById('exportBtn');
            const copyAllBtn = document.getElementById('copyAllBtn');

            // 重置显示状态
            loadingDiv.style.display = 'block';
            exportBtn.style.display = 'none';
            copyAllBtn.style.display = 'none';

            // 重置结果区域为表格结构
            resultDiv.innerHTML = `
                <h4 style="margin-bottom: 15px; color: var(--text-primary);">生成结果</h4>
                <div id="license-table-container" style="background: var(--bg-card); border: 2px solid var(--border-color); border-radius: 8px; overflow: auto; max-height: 400px;">
                    <table id="license-table" style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8fafc;">
                                <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">名</th>
                                <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">中间名</th>
                                <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">姓</th>
                                <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">出生日期</th>
                                <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">州</th>
                                <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">性别</th>
                                <th style="padding: 12px; border-bottom: 2px solid var(--border-color); text-align: left;">驾照号码</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            `;
            resultDiv.style.display = 'none';

            const tbody = document.querySelector('#license-table tbody');

            // 按行分割输入
            const lines = input.trim().split('\n');
            let hasValidResults = false;

            // 跳过可能的标题行
            const startIndex = lines[0].includes('FName') || lines[0].includes('姓') ? 1 : 0;

            for (let i = startIndex; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                const row = document.createElement('tr');
                const index = i - startIndex + 1;

                try {
                    // 先处理制表符，保留空字段
                    let processedLine = line.replace(/\t/g, '|TAB|'); // 用特殊标记替换制表符

                    // 识别州名部分
                    let stateMatch = null;
                    for (const [fullName, abbr] of Object.entries(stateNameMap)) {
                        const stateRegex = new RegExp(`\\b${fullName}\\b`, 'i');
                        if (processedLine.match(stateRegex)) {
                            stateMatch = { fullName: fullName, abbr: abbr };
                            break;
                        }
                    }

                    if (stateMatch) {
                        processedLine = processedLine.replace(new RegExp(`\\b${stateMatch.fullName}\\b`, 'i'), stateMatch.abbr);
                    }

                    // 标准化空格但保留制表符标记
                    processedLine = processedLine.replace(/\s+/g, ' ');

                    // 分割字段，保留空字段
                    let fields = processedLine.split('|TAB|').map(field => field.trim());

                    if (fields.length < 4) {
                        throw new Error('数据格式不正确，请确保至少包含：名字、姓氏、出生日期、州代码');
                    }

                    // 找到日期的位置
                    let dateIndex = -1;
                    for (let j = 0; j < fields.length; j++) {
                        if (fields[j].match(/\d{2}[\/\-]\d{2}[\/\-]\d{4}/)) {
                            dateIndex = j;
                            break;
                        }
                    }

                    if (dateIndex === -1) {
                        throw new Error('未找到有效的日期格式（应为MM/DD/YYYY）');
                    }

                    // 根据日期位置重新组织字段
                    let first = fields[0];
                    let middle = '';
                    let last = '';

                    if (dateIndex === 2) {
                        // 格式：名 姓 日期 州 性别 (无中间名)
                        last = fields[1];
                        middle = '';
                    } else if (dateIndex === 3) {
                        // 格式：名 中间名 姓 日期 州 性别 (有中间名，可能为空)
                        middle = fields[1] || ''; // 中间名可能为空
                        last = fields[2];
                    } else if (dateIndex === 4) {
                        // 格式：名 中间名1 中间名2 姓 日期 州 性别 (多个中间名)
                        middle = fields[1] || '';
                        last = fields[3];
                    } else {
                        throw new Error('数据格式不正确');
                    }

                    // 获取日期和后续字段
                    const dateStr = fields[dateIndex];
                    const stateInput = fields[dateIndex + 1];
                    const genderInput = fields[dateIndex + 2] || 'M';

                    // 处理日期
                    const dobMatch = dateStr.match(/(\d{2})[\/\-](\d{2})[\/\-](\d{4})/);
                    if (!dobMatch) {
                        throw new Error('日期格式错误');
                    }
                    const m = dobMatch[1];
                    const d = dobMatch[2];
                    const y = dobMatch[3];

                    // 验证和标准化
                    first = first.trim().toUpperCase();
                    middle = (middle || '').trim().toUpperCase();
                    // 中间名只保留首字母
                    if (middle && middle.length > 1) {
                        middle = middle.charAt(0);
                    }
                    last = last.trim().toUpperCase();

                    if (!first || !last) {
                        throw new Error('名字和姓氏为必填项');
                    }

                    // 处理州名
                    const stateKey = stateInput.trim().toUpperCase();
                    const state = stateNameMap[stateKey] || stateInput.trim().toUpperCase();
                    if (!stateNameMap[state]) {
                        throw new Error('未识别到支持的州');
                    }

                    // 处理性别
                    const genderKey = (genderInput || 'M').trim().toUpperCase();
                    const gender = genderMap[genderKey] || 'M';

                    // 生成驾照号码
                    const licenseNumber = stateConfig[state].generate(first, middle, last, m, d, y, gender);
                    hasValidResults = true;

                    // 添加结果行
                    row.innerHTML = `
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color);">${first}</td>
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color);">${middle || ''}</td>
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color);">${last}</td>
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color);">${m}/${d}/${y}</td>
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color);">${state}</td>
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color);">${gender}</td>
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color);">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span>${licenseNumber}</span>
                                <button onclick="copyLicense('${licenseNumber}')" style="padding: 4px 8px; background: var(--primary-color); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">复制</button>
                            </div>
                        </td>
                    `;
                } catch (error) {
                    row.innerHTML = `
                        <td colspan="6" style="padding: 12px; border-bottom: 1px solid var(--border-color); background: #fff2f0;">${line}</td>
                        <td style="padding: 12px; border-bottom: 1px solid var(--border-color); color: var(--error-color); background: #fff2f0;">❌ ${error.message}</td>
                    `;
                }

                tbody.appendChild(row);
            }

            loadingDiv.style.display = 'none';
            resultDiv.style.display = 'block';
            if (hasValidResults) {
                exportBtn.style.display = 'inline-block';
                copyAllBtn.style.display = 'inline-block';
            }
        }

        // 显示驾照复制状态
        function showLicenseCopyStatus(message, type = 'success') {
            const statusDiv = document.getElementById('license-copy-status');
            if (statusDiv) {
                const colorMap = {
                    success: 'var(--success-color)',
                    error: 'var(--error-color)',
                    warning: 'var(--warning-color)'
                };
                statusDiv.innerHTML = `<span style="color: ${colorMap[type] || colorMap.success};">${message}</span>`;
                // 3秒后清除状态
                setTimeout(() => {
                    statusDiv.innerHTML = '';
                }, 3000);
            }
        }

        // 复制单个驾照号码
        function copyLicense(licenseNumber) {
            navigator.clipboard.writeText(licenseNumber).then(() => {
                showLicenseCopyStatus('✅ 已复制驾照号码: ' + licenseNumber);
            }).catch(() => {
                showLicenseCopyStatus('❌ 复制失败，请手动复制', 'error');
            });
        }

        // 复制所有驾照号码
        function copyAllLicenses() {
            const table = document.getElementById('license-table');
            const licenses = [];
            for (let i = 1; i < table.rows.length; i++) {
                const row = table.rows[i];
                const licenseCell = row.cells[6]; // 驾照号码在第7列（索引6）
                if (licenseCell) {
                    const licenseSpan = licenseCell.querySelector('span');
                    if (licenseSpan) {
                        licenses.push(licenseSpan.textContent);
                    }
                }
            }

            if (licenses.length > 0) {
                navigator.clipboard.writeText(licenses.join('\n')).then(() => {
                    showLicenseCopyStatus(`✅ 已复制所有驾照号码 (${licenses.length}个)`);
                }).catch(() => {
                    showLicenseCopyStatus('❌ 复制失败，请手动复制', 'error');
                });
            } else {
                showLicenseCopyStatus('⚠️ 没有找到可复制的驾照号码', 'warning');
            }
        }

        // 导出CSV功能
        function exportResults() {
            const table = document.getElementById('license-table');
            let csv = [];

            // 添加表头
            const headers = [];
            for (const cell of table.rows[0].cells) {
                headers.push(cell.textContent);
            }
            csv.push(headers.join(','));

            // 添加数据行
            for (let i = 1; i < table.rows.length; i++) {
                const row = table.rows[i];
                const rowData = [];
                for (const cell of row.cells) {
                    let cellText = cell.textContent.replace(/"/g, '""');
                    // 清理复制按钮文本
                    cellText = cellText.replace('复制', '').trim();
                    rowData.push(`"${cellText}"`);
                }
                csv.push(rowData.join(','));
            }

            // 创建下载链接
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = '驾照号码生成结果.csv';
            link.click();
        }

        // ==================== 页面初始化 ====================

        // 绑定邮箱页面TOTP输入事件
        function bindTOTPEventsEmail() {
            const secretInputEmail = document.getElementById('secret-input-email');
            if (secretInputEmail) {
                secretInputEmail.addEventListener('input', function() {
                    clearTimeout(window.secretUpdateTimeoutEmail);
                    window.secretUpdateTimeoutEmail = setTimeout(() => {
                        updateTOTPSecretEmail();
                    }, 300);
                });

                secretInputEmail.addEventListener('paste', function() {
                    setTimeout(() => {
                        updateTOTPSecretEmail();
                    }, 50);
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化邮箱页面TOTP
            const savedSecretEmail = localStorage.getItem('totp-secret-email');
            const secretInputEmail = document.getElementById('secret-input-email');
            if (savedSecretEmail && secretInputEmail) {
                secretInputEmail.value = savedSecretEmail;
                initTOTPEmail(savedSecretEmail);
            }

            updateTOTPDisplayEmail();
            setInterval(updateTOTPDisplayEmail, 1000);
            bindTOTPEventsEmail();

            // 邮箱输入框回车事件
            const emailInput = document.getElementById('emailAddress');
            if (emailInput) {
                emailInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        checkEmails();
                    }
                });
            }

            // 驾照输入框快捷键
            const licenseInput = document.getElementById('license-input');
            if (licenseInput) {
                licenseInput.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.key === 'Enter') {
                        batchExtractLicense();
                    }
                });
            }

            // 初始化驾照生成器为单个模式
            switchLicenseMode('single');

            // 单个生成表单的回车事件
            const singleInputs = ['single-fname', 'single-mname', 'single-lname', 'single-birth'];
            singleInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            generateSingleLicense();
                        }
                    });
                }
            });

            // 单个生成下拉框的回车事件
            const singleSelects = ['single-state', 'single-gender'];
            singleSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    select.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            generateSingleLicense();
                        }
                    });
                }
            });

            console.log('🛠️ 综合工具箱已初始化完成');
        });
    </script>
</body>
</html>
